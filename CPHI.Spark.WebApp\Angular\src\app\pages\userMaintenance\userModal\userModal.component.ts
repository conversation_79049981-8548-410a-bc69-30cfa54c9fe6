import { Component, ElementRef, OnInit, ViewChild } from "@angular/core";
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { AppUserRole } from 'src/app/model/AppUserRole';
import { ApiAccessService } from "src/app/services/apiAccess.service";
import { CphPipe } from '../../../cph.pipe';
import { SalesRole, SiteVM } from '../../../model/main.model';
import { AutotraderService } from '../../../services/autotrader.service';
import { ConstantsService } from '../../../services/constants.service';
import { ClaimTypeAndValues } from "src/app/model/ClaimTypeAndValues";
import { UserAndLogin } from "src/app/model/UserAndLogin";
import { UserClaim } from "src/app/model/UserClaim";
import { SelectionsService } from '../../../services/selections.service';
import { UserMaintenanceService } from "../userMaintenance.service";
import { RetailerSite } from "src/app/model/RetailerSite";


@Component({
  selector: 'userModal',
  templateUrl: './userModal.component.html',
  styleUrls: ['./userModal.component.scss']
})


export class UserModalComponent implements OnInit {
  @ViewChild('modalRef', { static: true }) modalRef: ElementRef;

  userAndLoginOnInit: UserAndLogin;
  userAndLogin: UserAndLogin;

  initiallyLoadedEmail: string;

  monthNames = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];
  allSites: SiteVM[]
  salesRoles: SalesRole[];

  claims: UserClaim[];
  claimsOnInit: UserClaim[];

  isManager: boolean = false;
  public allSalesRoles = this.constants.environment.userSetup_allSalesRoles;

  errorMessage: string = null;
  showEmailDomainWarning: boolean = false;
  emailDomainWarningMessage: string = '';

  constructor(
    public constants: ConstantsService,
    public selections: SelectionsService,
    public modalService: NgbModal,
    public analysis: AutotraderService,
    public cphPipe: CphPipe,
    public apiAccess: ApiAccessService,
    private service: UserMaintenanceService,
    private activeModal: NgbActiveModal

  ) { }

  ngOnInit() {
    this.setIsManagerBoolean();
    this.userAndLoginOnInit = this.constants.clone(this.userAndLogin);
  }

  // This is to deal with the nullable boolean that is causing issues
  private setIsManagerBoolean()
  {
    if (typeof this.userAndLogin.IsTMgr === 'string') {
      this.userAndLogin.IsTMgr = (this.userAndLogin.IsTMgr === 'true');
    }

    if (this.userAndLogin.IsTMgr === true) {  // Use strict equality check
      this.isManager = true;
    } else {
      this.isManager = false;
    }
  }


  public chooseSite(site: SiteVM) {
    this.userAndLogin.site = site;
    this.userAndLogin.SiteId = site.SiteId
  }

  public chooseRetailerSite(site: RetailerSite) {
    this.userAndLogin.chosenRetailerSite = site;
  }

  public resetModal() {
    this.userAndLogin.site = null;
    this.userAndLogin.chosenSites = [];
    this.userAndLogin.chosenRetailerSite = null;
    this.showEmailDomainWarning = false;
    this.emailDomainWarningMessage = '';
  }

  public initialiseModal(user: UserAndLogin) {

    this.userAndLogin = user;

    //this.userAndLoginClone = JSON.parse(JSON.stringify(user));

    // If there is a person line but not a user, let's get their email address
    if(this.userAndLogin.PersonId)
    {
      this.initiallyLoadedEmail = this.getEmailForPerson(this.userAndLogin.PersonId);
    }

    this.resetModal()

    this.allSites = this.constants.clone(this.constants.Sites)
    this.allSites.map(x => x.isSelected = false)

    user.initialise(this.constants.Sites, this.constants.RetailerSites, this.constants.Roles);

    this.buildClaims(user.Claims);
    this.claimsOnInit = this.constants.clone(this.claims);

    this.salesRoles = user.SalesRoles;
  }

  getEmailForPerson(personId: number): string | null {
    const matches = this.service.rowData.filter(x => x.PersonId == personId);
    if (matches.length > 0) {
      return matches[0].Email;
    } else {
      return null;
    }
  }

  buildClaims(userClaims: UserClaim[]): void {
    this.claims = [];

    this.constants.ClaimTypes.forEach((claimType: ClaimTypeAndValues) => {
      const userClaim = userClaims.find(claim => claim.ClaimType == claimType.ClaimType);
      const claimTypeIsBool = claimType.ClaimValues == 'true,false';
      if (userClaim != null) {
        //we have a user claim
        this.claims.push({ ClaimType: userClaim.ClaimType, ClaimValue: userClaim.ClaimValue, ClaimChoices:  claimType.ClaimValues.split(','), isBoolean: claimTypeIsBool })
      }
      else {
        this.claims.push({ ClaimType: claimType.ClaimType, ClaimValue: claimTypeIsBool ? "false" : 'none', ClaimChoices: claimType.ClaimValues.split(','), isBoolean: claimTypeIsBool })
      }
    })
  };

  toggleTMgrStatus(): void {
    this.userAndLogin.IsTMgr = !this.userAndLogin.IsTMgr;
    this.checkOkToSave();
  }

  chooseRole(role: AppUserRole): void {
    this.userAndLogin.RoleName = role.Name;
    this.userAndLogin.role = role;
  }


  chooseSalesRole(salesRole: string, monthNumber: number) : void {
    for (let i = 1; i <= 12; i++) {

      if (i >= monthNumber) {
        // If a new entry
        if (this.salesRoles.filter(x => x.Month == i).length == 0) {

          let newRole: SalesRole = {
            Id: 0,
            Month: i,
            Year: this.constants.todayStart.getFullYear(),
            SiteId: this.userAndLogin.SiteId,
            //SiteDescription :  this.chosenSite.SiteDescription,
            Role: salesRole,
            personId: this.userAndLogin.PersonId
          };

          this.salesRoles.push(newRole);
        }
        // If updating an entry
        else {
          const element = this.salesRoles.filter(x => x.Month == i)[0];

          if (element.Month >= monthNumber) {
            element.Role = salesRole;
          }
        }
      }

    }

    this.salesRoles.sort((a, b) => a.Month - b.Month);

    // this.checkOkToSave();
  }

  public getRoleForMonth(monthNumber: number): string {
    const role = this.salesRoles.find(s => s.Month == monthNumber);
    if(role == null) return null;
    if(role.Role == null && role.Month != null) return 'None';
    return role.Role;
  }

  public getSiteForMonth(monthNumber: number): string {
    const role = this.salesRoles.find(s => s.Month == monthNumber)?.SiteDescription;
    return role;
  }

  setClaim(claimType: string, claimValue: string): void {
    let commissionClaim = this.claims.find(x => x.ClaimType == claimType);
    commissionClaim.ClaimValue = claimValue;
  }

  onUpdateSites(sites: SiteVM[]): void {
    let siteIds: number[] = sites.map(x => x.SiteId);
    this.userAndLogin.Sites = siteIds.join(',')
    this.userAndLogin.chosenSites = sites
  }

  saveUser() {

    this.userAndLogin.IsTMgr = this.isManager;

    this.modalService.dismissAll();
    this.selections.triggerSpinner.next({ show: true, message: this.constants.translatedText.Common_Loading });

    //process these for the back end
    this.userAndLogin.convertChoicesBackToIds(this.claims);
    this.apiAccess.post('api/User', 'SaveUserAndLogin', this.userAndLogin).subscribe((data) => {

      const message = this.userAndLogin.PersonId ? 'Saved changes to user' : 'Saved new user';
      this.constants.toastSuccess(message)

      if(this.userAndLogin.AppUserId){

        this.userAndLogin.AppUserId = data.AppUserId;
        this.userAndLogin.PersonId = data.PersonId;
        this.userAndLogin.UserName = this.userAndLogin.Email;
        this.service.updateGridWithNewData(this.userAndLogin)
      }else{

        //new user
        this.userAndLogin.AppUserId = data.AppUserId;
        this.userAndLogin.PersonId = data.PersonId;
        this.userAndLogin.UserName = this.userAndLogin.Email;
        this.service.addToGrid(this.userAndLogin);
      }

      this.selections.triggerSpinner.next({ show: false });

    }, (e) => {
      this.constants.toastDanger('Failed to save User')
    })


  }

  checkOkToDelete() : boolean {
    return this.validateData();
  }

  checkOkToSave() : boolean {

    if(!this.userAndLogin.AppUserId)  // Creating a new user
    {
      return this.validateData();
    }
    else if(this.hasDataChanged()) // Updating an existing user, lets check if data has changed
    {
      return this.validateData();
    }

  }

  private hasDataChanged(): boolean
  {
    const hasDifferences = JSON.stringify(this.userAndLoginOnInit) !== JSON.stringify(this.userAndLogin);

    if(hasDifferences)
    {
      return true;
    }
    // Also check if claims are different
    else
    {

      // console.log(this.userAndLoginOnInit.Claims, "this.userAndLoginOnInit.Claims");
      // console.log(this.userAndLogin.Claims, "this.userAndLogin.Claims");
      // console.log(this.claimsOnInit, "this.claimsOnInit");
      // console.log(this.claims, "this.claims");

      const areClaimsEqual = this.claimsAreEqual(
        this.claimsOnInit,
        this.claims
      );

      if(!areClaimsEqual)
      {
        return true;
      }
    }

    this.errorMessage = 'No changes to save.';
    return false;
  }

  private claimsAreEqual(claims1: UserClaim[], claims2: UserClaim[]): boolean {
    if (claims1.length !== claims2.length) return false;

    return claims1.every((claim1, index) => {
      const claim2 = claims2[index];
      return claim1.ClaimType === claim2.ClaimType && claim1.ClaimValue === claim2.ClaimValue;
    });
  }

  private validateData() : boolean {

    this.errorMessage = null;

    if(this.userAndLogin.IsTMgr == null)
    {
      this.userAndLogin.IsTMgr = false;
    }
    else
    {
      this.userAndLogin.IsTMgr = Boolean(this.userAndLogin.IsTMgr);
    }

    if(this.isTakenEmail(this.userAndLogin.Email))
    {
      this.errorMessage = 'The email is already taken.'
      return false;
    }

    if(this.isInvalidEmail(this.userAndLogin.Email))
    {
      this.errorMessage = 'The email is invalid.'
      return false;
    }

    if(!this.userAndLogin.role)
    {
      this.errorMessage = 'Missing or invalid user role.'
      return false;
    }

    if(!this.userAndLogin.site)
    {
      this.errorMessage = 'Missing home site.'
      return false;
    }

    if(!this.userAndLogin.chosenRetailerSite && this.constants.environment.userModal_showRetailSiteErrorMessage)
    {
      this.errorMessage = 'Missing retailer site.'
      return false;
    }

    if(!this.userAndLogin.Name)
    {
      this.errorMessage = 'Missing name.'
      return false;
    }

    if(!this.userAndLogin.Email)
    {
      this.errorMessage = 'Missing email.'
      return false;
    }

    if(!this.userAndLogin.JobTitle)
    {
      this.errorMessage = 'Missing job title.'
      return false;
    }

    if(this.userAndLogin.chosenSites.length == 0)
    {
      this.errorMessage = 'No sites added. You may need to click OK on the sites dropdown.'
      return false;
    }

    if (!this.userAndLogin.chosenSites.map(x=>x.SiteId).includes(this.userAndLogin.site.SiteId))
    {
      this.errorMessage = 'Home site needs to also be in sites list.'
      return false;
    }

    if(this.constants.environment.userModal_showRetailSiteErrorMessage)
    {
      if(this.userAndLogin.chosenRetailerSite.Site_Id != this.userAndLogin.site.SiteId)
      {
        this.errorMessage = 'Retailer site needs to match Home site.'
        return false;
      }
    }

    this.errorMessage = null;
    return true;
  }

  isInvalidEmail(email: string): boolean {
    const invalidEmail = ! /^.+@.+\..+$/.test(email);
    return invalidEmail
  }

  isTakenEmail(email: string): boolean {
    if (!email) {
      return false;
    }

    // Convert the user’s originally loaded email to lowercase (or empty string if undefined)
    const initialEmail = this.initiallyLoadedEmail?.toLowerCase() || "";

    // Build a list of all existing emails (lowercase), excluding the user's old email
    const existingEmails = this.service.rowData
      .map(x => x.Email ? x.Email.toLowerCase() : null)
      .filter(x => x !== null)
      .filter(x => x !== initialEmail);

    // Return true if the given email (lowercased) appears in that list
    return existingEmails.includes(email.toLowerCase());
  }

  checkEmailDomainForDealerGroup(email: string): void {
    this.showEmailDomainWarning = false;
    this.emailDomainWarningMessage = '';

    if (!email || !email.includes('@')) {
      return;
    }

    const emailDomain = email.toLowerCase().split('@')[1];
    const dealerGroupName = this.selections.dealerGroupName;

    console.log(dealerGroupName, "dealerGroupName");

    // Define expected email domains for each dealer group
    const dealerGroupEmailDomains: { [key: string]: string[] } = {
      'RRGUK': ['renault.co.uk', 'rrg.co.uk'],
      'RRG Spain': ['renault.es', 'rrg.es'],
      'Vindis': ['vindis.co.uk'],
      'Sytner': ['sytner.co.uk'],
      'Jardine': ['jardine.co.uk'],
      'BrindleyGroup': ['brindley.co.uk'],
      'SturgessGroup': ['sturgess.co.uk'],
      'PentagonGroup': ['pentagon.co.uk'],
      'EMGGroup': ['emggroup.co.uk'],
      'AcornGroup': ['acorngroup.co.uk'],
      'Enterprise': ['enterprise.co.uk'],
      'JCT600': ['jct600.co.uk'],
      'Citygate': ['citygate.co.uk'],
      // Add more dealer groups and their expected domains as needed
    };

    const expectedDomains = dealerGroupEmailDomains[dealerGroupName];

    if (expectedDomains && !expectedDomains.includes(emailDomain)) {
      this.showEmailDomainWarning = true;
      this.emailDomainWarningMessage = `This email domain (@${emailDomain}) doesn't match the expected domains for ${dealerGroupName} (${expectedDomains.map(d => '@' + d).join(', ')}). Are you sure you want to continue?`;
    }
  }

  onEmailChange(): void {
    this.checkEmailDomainForDealerGroup(this.userAndLogin.Email);
    this.checkOkToSave();
  }

  dismissEmailDomainWarning(): void {
    this.showEmailDomainWarning = false;
    this.emailDomainWarningMessage = '';
  }



  getMonthName(numberNumber: number) {
    return this.monthNames[numberNumber - 1]
  }

  chooseSiteSalesrole(site: SiteVM, monthNumber: number) {
    for (let i = 1; i <= 12; i++) {

      if (i >= monthNumber) {
        if (this.salesRoles.filter(x => x.Month == i).length == 0) {

          let newRole: SalesRole = {
            Id: 0,
            Month: i,
            Year: this.constants.todayStart.getFullYear(),
            SiteId: site.SiteId,
            SiteDescription: site.SiteDescription,
            Role: null,
            personId: this.userAndLogin.PersonId
          };

          this.salesRoles.push(newRole);
        }
        else {
          const element = this.salesRoles.filter(x => x.Month == i)[0];

          if (element.Month >= monthNumber) {
            element.SiteId = site.SiteId;
            element.SiteDescription = site.SiteDescription;
          }
        }
      }

    }

    this.salesRoles.sort((a, b) => a.Month - b.Month);
  }

  toggleBooleanClaim(claim: UserClaim) {

    if (claim.ClaimValue == 'true') {
      claim.ClaimValue = 'false';
    } else {
      claim.ClaimValue = 'true'
    }
  }


  cancelModal() {
    this.modalService.dismissAll()
  }

  saveButtonMessage() {
    if (this.userAndLogin.AppUserId) { return 'Update user' }
    return 'Save new user'
  }

  maybeDeleteUser(){
    this.activeModal.close();
    this.service.maybeDelete(this.userAndLogin)
  }

 updateSalesExecAndClaims(): void {
  this.userAndLogin.IsSalesExec = !this.userAndLogin.IsSalesExec;

  const booleanClaims = [
    'seeDashboard',
    'seeDealsDoneThisWeek',
    'seeDealsForTheMonth',
    'seeHandoverDiary',
    'seeOrderbook',
    'seePerformanceLeague',
    'seeWhiteboard'
  ];

  const specificValueClaims: Record<string, string> = {
    'commissionAuthority': 'selfOnly',
    'salesExecReview': 'submitter'
  };

  if (!this.constants.environment.sideMenu_salesExecReview) {
    delete specificValueClaims.salesExecReview;
  }

  this.updateClaims(this.userAndLogin.IsSalesExec, booleanClaims, specificValueClaims);
  this.checkOkToSave();
}

updateManagerStatusAndClaims(): void {
  this.isManager = !this.isManager;
  this.userAndLogin.IsTMgr = this.isManager;

  const booleanClaims = [
    'seeDashboard',
    'seeDealsDoneThisWeek',
    'seeDealsForTheMonth',
    'seeHandoverDiary',
    'seeOrderbook',
    'seePerformanceLeague',
    'seeWhiteboard',
    'seeStockList',
    'seeStockPricing',
    'seePerformanceTrends',
    'accessReportCentre',
    'editExecManagerMappings',
    'canReviewStockPrices',
    'canActionStockPrices',
    'canEditPricingStrategy'
  ];

  const specificValueClaims: Record<string, string> = {
    'commissionAuthority': 'review',
    'salesExecReview': 'reviewer'
  };

  this.updateClaims(this.isManager, booleanClaims, specificValueClaims);
  this.checkOkToSave();
}

private updateClaims(flag: boolean, booleanClaims: string[], specificValueClaims: Record<string, string>): void {
  booleanClaims.forEach(claimType => {
    const claim = this.claims.find(c => c.ClaimType === claimType);
    if (claim) {
      claim.ClaimValue = flag ? 'true' : 'false';
    }
  });

  Object.entries(specificValueClaims).forEach(([claimType, value]) => {
    const claim = this.claims.find(c => c.ClaimType === claimType);
    if (claim) {
      claim.ClaimValue = flag ? value : 'none';
    }
  });
}


}


