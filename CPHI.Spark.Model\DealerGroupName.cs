using CPHI.Spark.Model;
using System;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CPHI.Spark.Model
{
   public enum DealerGroupName // By DealerGroupId
   {
      //None = 0,
      RRGUK = 1,
      RRGSpain = 2,
      <PERSON><PERSON> = 3,
      <PERSON>ardine = 4,
      PinkStones = 5,
      LMC = 6,
      V12 = 7,
      MJMotorCo = 8,
      BrindleyGroup = 9,
      KCSOfSurrey = 10,
      WaylandsGroup = 11,
      SturgessGroup = 12,
      JJPremiumCars = 13,
      PentagonGroup = 14,
      HippoApproved = 15,
      LMCOfFarnham = 16,
      Startin = 17,
      OakwoodMotorCo = 18,
      SparshattsGroup = 19,
      BellsCrossgar = 20,
      CroxdaleGroup = 21,
      EMGGroup = 22,
      AcornGroup = 23,
      Lithia = 24,
      FordsOfWinsford = 25,
      LSH = 26,
      <PERSON><PERSON>ner = 27,
      Enterprise = 28,
      <PERSON><PERSON><PERSON><PERSON> = 29,
      <PERSON>uneaton = 30,
      Carco = 31,
      SRMotorGroup = 32,
      CarWorld = 33,
      WJKing = 34,
      ArthursCars = 35,
      GravelHillCars = 36,
      BigMotoringWorld = 37,
      JCT600 = 38,
      Citygate = 39,
      <PERSON>ndFord=40,
      HiltonGarage=41


      //This should now be the only place in the back end that we need to add a new group
   }
}
